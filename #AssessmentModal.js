//AssessmentModal
import React, { useState } from "react";
import { Assessment } from "@/entities/Assessment";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckCircle, Send } from "lucide-react";

export default function AssessmentModal({ isOpen, onClose }) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    business_type: "",
    help_needed: [],
    current_software: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const helpOptions = [
    { id: "cleanup", label: "Clean-Up" },
    { id: "payroll", label: "Payroll" },
    { id: "reconciliation", label: "Reconciliation" },
    { id: "compliance", label: "Compliance" },
    { id: "other", label: "Something Else" }
  ];

  const handleHelpNeededChange = (optionId, checked) => {
    setFormData(prev => ({
      ...prev,
      help_needed: checked 
        ? [...prev.help_needed, optionId]
        : prev.help_needed.filter(id => id !== optionId)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await Assessment.create(formData);
      setIsSubmitted(true);
    } catch (error) {
      console.error("Error submitting assessment:", error);
    }

    setIsSubmitting(false);
  };

  const handleClose = () => {
    setIsSubmitted(false);
    setFormData({
      name: "",
      email: "",
      business_type: "",
      help_needed: [],
      current_software: ""
    });
    onClose();
  };

  if (isSubmitted) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Thank You!
            </h3>
            <p className="text-gray-600 mb-6">
              Your assessment has been submitted. You'll hear from us within 24 hours with personalized recommendations for your business.
            </p>
            <Button 
              onClick={handleClose}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full font-semibold"
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-gray-900 text-center">
            Let's See Where You Stand
          </DialogTitle>
          <p className="text-gray-600 text-center">
            This quick assessment helps us understand your needs and provide personalized recommendations.
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name *</Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              required
              className="rounded-lg"
              placeholder="Your full name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              required
              className="rounded-lg"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="business_type">Business Type *</Label>
            <Select 
              value={formData.business_type} 
              onValueChange={(value) => setFormData({...formData, business_type: value})}
            >
              <SelectTrigger className="rounded-lg">
                <SelectValue placeholder="Select your business type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trade">Trade (Electrician, Plumber, etc.)</SelectItem>
                <SelectItem value="real_estate">Real Estate / Property Management</SelectItem>
                <SelectItem value="online_business">Online Business</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label>What Do You Need Help With? (Select all that apply)</Label>
            <div className="grid grid-cols-2 gap-3">
              {helpOptions.map((option) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={option.id}
                    checked={formData.help_needed.includes(option.id)}
                    onCheckedChange={(checked) => handleHelpNeededChange(option.id, checked)}
                  />
                  <Label htmlFor={option.id} className="text-sm font-normal">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="current_software">Current Bookkeeping Software</Label>
            <Input
              id="current_software"
              type="text"
              value={formData.current_software}
              onChange={(e) => setFormData({...formData, current_software: e.target.value})}
              className="rounded-lg"
              placeholder="QuickBooks, Excel, Xero, etc. (or 'None')"
            />
          </div>

          <Button 
            type="submit"
            disabled={isSubmitting || !formData.name || !formData.email || !formData.business_type}
            className="w-full bg-gradient-to-r from-red-400 to-pink-500 hover:from-red-500 hover:to-pink-600 text-white py-3 rounded-full font-semibold text-lg disabled:opacity-50"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Sending Assessment...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Send className="w-5 h-5" />
                Send My Assessment
              </div>
            )}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}