//Pricing


import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, Star, Crown, Zap } from "lucide-react";

export default function PricingSection({ onGetAssessment }) {
  const plans = [
    {
      name: "Basic",
      subtitle: "Startup",
      description: "For solopreneurs and new businesses just getting started",
      icon: Zap,
      iconColor: "text-blue-600",
      cardClass: "border-blue-200 hover:border-blue-300",
      features: [
        "Monthly bank reconciliation",
        "Transaction categorization",
        "Reconciliation reports",
        "Balance Sheet reports"
      ],
      popular: false
    },
    {
      name: "Growth",
      subtitle: "Scaling",
      description: "For growing service providers and contractors",
      icon: Star,
      iconColor: "text-purple-600",
      cardClass: "border-purple-300 hover:border-purple-400 ring-2 ring-purple-200",
      features: [
        "Up to 3 accounts",
        "150 transactions",
        "Credit card reconciliation",
        "Monthly financials",
        "Payroll support (5 employees max)",
        "Quarterly financial review"
      ],
      popular: true
    },
    {
      name: "Success",
      subtitle: "Established",
      description: "For advanced operations or messy books",
      icon: Crown,
      iconColor: "text-amber-600",
      cardClass: "border-amber-200 hover:border-amber-300",
      features: [
        "Unlimited accounts/transactions",
        "Full payroll (up to 15 employees)",
        "Job costing",
        "Monthly financials & analysis",
        "Quarterly tax prep support",
        "Monthly strategy call"
      ],
      popular: false
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Do The <span className="gradient-text">Math</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            No hidden fees, no surprises. Choose the perfect plan that grows with your business.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {plans.map((plan, index) => (
            <Card 
              key={plan.name}
              className={`relative ${plan.cardClass} hover-lift transition-all duration-300 ${plan.popular ? 'transform scale-105' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </div>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gray-50 flex items-center justify-center`}>
                  <plan.icon className={`w-8 h-8 ${plan.iconColor}`} />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">
                  {plan.name}
                </CardTitle>
                <div className="text-sm text-gray-500 font-medium mb-2">
                  {plan.subtitle}
                </div>
                <p className="text-gray-600 text-sm">
                  {plan.description}
                </p>
              </CardHeader>

              <CardContent className="pt-0">
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button 
                  onClick={onGetAssessment}
                  className={`w-full rounded-full font-semibold py-3 transition-all duration-200 ${
                    plan.popular 
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg' 
                      : 'bg-gray-900 hover:bg-gray-800 text-white'
                  }`}
                >
                  Get Started
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <p className="text-gray-600 mb-4">
            Not sure what you need?
          </p>
          <Button 
            onClick={onGetAssessment}
            variant="outline"
            className="border-2 border-blue-500 text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-full font-semibold"
          >
            Take the Free Assessment
          </Button>
        </div>
      </div>
    </section>
  );
}