import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, BookOpen, AlertTriangle, CheckSquare } from "lucide-react";

export default function BlogSection() {
  const posts = [
    {
      title: "What a Bookkeeping Clean-Up Really Fixes (and Costs)",
      excerpt: "From messy records to clarity in one project.",
      icon: BookOpen,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-50",
      readTime: "5 min read"
    },
    {
      title: "Red Flags in Your Financials – And How to Catch Them Early",
      excerpt: "Spot trouble before the IRS or your cash flow does.",
      icon: Alert<PERSON>riangle,
      iconColor: "text-red-600",
      bgColor: "bg-red-50",
      readTime: "7 min read"
    },
    {
      title: "How Trades Pros Can Stay Compliant Without Doing More Paperwork",
      excerpt: "Automate and delegate your way to clean books.",
      icon: CheckSquare,
      iconColor: "text-green-600",
      bgColor: "bg-green-50",
      readTime: "6 min read"
    }
  ];

  return (
    <div className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Latest Insights
          </h2>
          <p className="mt-2 text-lg leading-8 text-gray-600">
            Expert advice to help you manage your business finances better.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {posts.map((post, index) => (
            <Card key={index} className="flex flex-col items-start">
              <CardHeader className="w-full">
                <div className={`w-12 h-12 rounded-lg ${post.bgColor} flex items-center justify-center mb-4`}>
                  <post.icon className={`w-6 h-6 ${post.iconColor}`} />
                </div>
                <CardTitle className="text-lg font-semibold leading-6 text-gray-900">
                  {post.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-base leading-7 text-gray-600">{post.excerpt}</p>
                <div className="mt-8 flex items-center gap-x-4">
                  <Button variant="link" className="text-sm font-semibold leading-6">
                    Read more <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  <span className="text-sm text-gray-500">{post.readTime}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
