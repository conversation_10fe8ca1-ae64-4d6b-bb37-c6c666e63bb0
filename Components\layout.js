//layout	                                                                                                         

import React from "react";
import { Link, useLocation } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { Menu, X, Phone, Mail, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function Layout({ children, currentPageName }) {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <div className="min-h-screen bg-white">
      <style>{`
        :root {
          --primary-blue: #0f4c75;
          --accent-teal: #3282b8;
          --warm-coral: #ff6b6b;
          --soft-gray: #f8fafc;
          --text-gray: #64748b;
        }
        
        * {
          scroll-behavior: smooth;
        }
        
        .gradient-text {
          background: linear-gradient(135deg, var(--primary-blue), var(--accent-teal));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        .hover-lift {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .hover-lift:hover {
          transform: translateY(-4px);
          box-shadow: 0 20px 40px rgba(15, 76, 117, 0.15);
        }
      `}</style>

      {/* Sticky Navigation */}
      <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-md border-b border-gray-100 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="flex items-center gap-3">
                <img 
                  src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/5d277938d_logoQB.png" 
                  alt="QuickBooks ProAdvisor Certified" 
                  className="w-8 h-8"
                />
                <div className="text-2xl font-bold text-violet-700">
                  April Does Data
                </div>
              </div>
              <div className="hidden md:block ml-8">
                <div className="flex items-center space-x-8">
                  <button 
                    onClick={() => scrollToSection('home')}
                    className="text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                  >
                    Home
                  </button>
                  <button 
                    onClick={() => scrollToSection('services')}
                    className="text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                  >
                    Services
                  </button>
                  <button 
                    onClick={() => scrollToSection('pricing')}
                    className="text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                  >
                    Packages
                  </button>
                  <button 
                    onClick={() => scrollToSection('blog')}
                    className="text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                  >
                    Blog
                  </button>
                  <button 
                    onClick={() => scrollToSection('testimonials')}
                    className="text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                  >
                    Testimonials
                  </button>
                </div>
              </div>
            </div>

            <div className="hidden md:block">
              <div className="flex items-center gap-6">
                <a href="tel:************" className="flex items-center gap-2 text-gray-700 hover:text-blue-600 font-semibold transition-colors">
                  <Phone className="w-4 h-4" />
                  ************
                </a>
                <Button 
                  onClick={() => scrollToSection('assessment')}
                  className="bg-gradient-to-r from-violet-700 to-pink-600 hover:from-violet-800 hover:to-pink-700 text-white px-6 py-2 rounded-full font-semibold transform transition-all duration-200 hover:scale-105 shadow-lg"
                >
                  Free Assessment
                </Button>
              </div>
            </div>

            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-700 hover:text-blue-600 transition-colors duration-200"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-100">
            <div className="px-4 pt-2 pb-3 space-y-1">
              <button 
                onClick={() => scrollToSection('home')}
                className="block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium"
              >
                Home
              </button>
              <button 
                onClick={() => scrollToSection('services')}
                className="block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium"
              >
                Services
              </button>
              <button 
                onClick={() => scrollToSection('pricing')}
                className="block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium"
              >
                Packages
              </button>
              <button 
                onClick={() => scrollToSection('blog')}
                className="block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium"
              >
                Blog
              </button>
              <button 
                onClick={() => scrollToSection('testimonials')}
                className="block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium"
              >
                Testimonials
              </button>
              <div className="border-t border-gray-100 my-2"></div>
              <a 
                href="tel:************" 
                className="flex items-center justify-center gap-2 px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium"
              >
                <Phone className="w-4 h-4" />
                ************
              </a>
              <div className="pt-2">
                <Button 
                  onClick={() => scrollToSection('assessment')}
                  className="w-full bg-gradient-to-r from-violet-700 to-pink-600 hover:from-violet-800 hover:to-pink-700 text-white rounded-full font-semibold"
                >
                  Free Assessment
                </Button>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Main Content */}
      <main className="pt-16">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-slate-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center gap-3 mb-4">
                <img 
                  src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/5d277938d_logoQB.png" 
                  alt="QuickBooks ProAdvisor Certified" 
                  className="w-8 h-8"
                />
                <div className="text-2xl font-bold text-white">
                  April Does Data
                </div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md">
                Expert remote bookkeeping for tradespeople, landlords, and small business owners. 
                Clean books, clear vision, confident decisions.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-3 text-gray-300">
                  <Phone className="w-4 h-4" />
                  <span>************</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <MapPin className="w-4 h-4" />
                  <span>Remote Services Nationwide</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-white mb-4">Quick Links</h3>
              <div className="space-y-2">
                <button 
                  onClick={() => scrollToSection('services')}
                  className="block text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Services
                </button>
                <button 
                  onClick={() => scrollToSection('pricing')}
                  className="block text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Packages
                </button>
                <button 
                  onClick={() => scrollToSection('blog')}
                  className="block text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Blog
                </button>
                <button 
                  onClick={() => scrollToSection('testimonials')}
                  className="block text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Testimonials
                </button>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-white mb-4">Get Started</h3>
              <Button 
                onClick={() => scrollToSection('assessment')}
                className="w-full bg-gradient-to-r from-violet-700 to-pink-600 hover:from-violet-800 hover:to-pink-700 text-white rounded-full font-semibold"
              >
                Free Assessment
              </Button>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 April Does Data. All rights reserved.</p>
          </div>

          <div className="space-y-2 mt-4">
            <div className="flex items-center gap-4">
              <a href="https://facebook.com/aprildoesdata" className="text-gray-300 hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
              </a>
              <a href="https://twitter.com/aprildoesdata" className="text-gray-300 hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
              </a>
              <a href="https://linkedin.com/in/aprildoesdata" className="text-gray-300 hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle></svg>
              </a>
              <a href="https://instagram.com/aprildoesdata" className="text-gray-300 hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
