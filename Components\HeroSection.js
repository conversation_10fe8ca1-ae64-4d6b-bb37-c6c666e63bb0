//Hero
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Calculator, TrendingUp, Shield } from "lucide-react";

export default function HeroSection({ onGetAssessment }) {
  return (
    <section id="home" className="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 pt-12 pb-20">
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
        <div className="absolute -bottom-8 left-1/2 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-2000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="text-center lg:text-left">
            <div className="mb-6">
              <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-blue-100 text-sm font-medium text-blue-700 mb-6">
                <Shield className="w-4 h-4" />
                Certified QuickBooks ProAdvisor
              </div>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Clean Books.{" "}
              <span className="gradient-text">Clear Vision.</span>{" "}
              <span className="text-blue-600">Confident Decisions.</span>
            </h1>

            <p className="text-xl text-gray-600 mb-8 max-w-2xl">
              Remote bookkeeping tailored for tradespeople, landlords, and small business owners 
              who need more than just spreadsheets.
 