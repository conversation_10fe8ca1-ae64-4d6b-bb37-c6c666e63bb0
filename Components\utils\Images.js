// Create a centralized image management system
const Images = {
  // Logo and branding
  logo: "https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/5d277938d_logoQB.png",
  
  // Hero section
  heroImage: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&h=600&fit=crop&crop=center",
  
  // Authority section
  authorityImage: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=300&fit=crop&crop=center",
  
  // Testimonials
  testimonialAvatar1: "https://randomuser.me/api/portraits/women/32.jpg",
  testimonialAvatar2: "https://randomuser.me/api/portraits/men/47.jpg",
  testimonialAvatar3: "https://randomuser.me/api/portraits/women/65.jpg",
  
  // Partners and integrations
  quickbooksLogo: "https://logos-world.net/wp-content/uploads/2021/03/QuickBooks-Logo.png",
  
  // Social icons can be imported from lucide-react instead of using external images
};

export default Images;