// Utility functions for the application

/**
 * Creates a URL for a page
 * @param {string} pageName - The name of the page
 * @param {Object} params - URL parameters
 * @returns {string} The formatted URL
 */
export const createPageUrl = (pageName, params = {}) => {
  const baseUrl = `/${pageName.toLowerCase().replace(/\s+/g, '-')}`;
  
  const queryParams = Object.keys(params).length 
    ? '?' + new URLSearchParams(params).toString() 
    : '';
    
  return baseUrl + queryParams;
};

/**
 * Format currency values
 * @param {number} amount - The amount to format
 * @param {string} currency - Currency code (default: USD)
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
};

/**
 * Truncate text to a specific length
 * @param {string} text - The text to truncate
 * @param {number} length - Maximum length
 * @returns {string} Truncated text
 */
export const truncateText = (text, length = 100) => {
  if (text.length <= length) return text;
  return text.slice(0, length) + '...';
};