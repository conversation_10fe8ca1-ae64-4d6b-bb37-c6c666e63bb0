import React, { useState } from "react";
import HeroSection from "@/components/sections/HeroSection";
import PricingSection from "@/components/sections/PricingSection";
import TestimonialSection from "@/components/sections/TestimonialSection";
import BlogSection from "@/components/sections/BlogSection";
import AuthoritySection from "@/components/sections/AuthoritySection";
import CTASection from "@/components/sections/CTASection";
import AssessmentModal from "@/components/modals/AssessmentModal";
import Layout from "@/components/layout/Layout";

export default function Home() {
  const [isAssessmentOpen, setIsAssessmentOpen] = useState(false);

  const handleGetAssessment = () => {
    setIsAssessmentOpen(true);
  };

  return (
    <Layout currentPageName="Home">
      <div className="min-h-screen">
        <HeroSection onGetAssessment={handleGetAssessment} />
        <AuthoritySection />
        <PricingSection onGetAssessment={handleGetAssessment} />
        <TestimonialSection />
        <BlogSection />
        <CTASection onGetAssessment={handleGetAssessment} />
        
        <div id="assessment">
          <AssessmentModal 
            isOpen={isAssessmentOpen} 
            onClose={() => setIsAssessmentOpen(false)} 
          />
        </div>
      </div>
    </Layout>
  );
}
