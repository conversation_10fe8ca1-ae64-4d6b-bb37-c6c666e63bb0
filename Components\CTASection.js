import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle } from "lucide-react";
import Images from "@/utils/Images";

export default function CTASection({ onGetAssessment }) {
  const benefits = [
    "Free 30-minute consultation",
    "Custom recommendations",
    "No obligation",
    "Same-day response"
  ];

  return (
    <section className="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-black opacity-10"></div>
        <div className="absolute top-10 left-10 w-72 h-72 bg-white rounded-full mix-blend-overlay filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-72 h-72 bg-white rounded-full mix-blend-overlay filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
          Ready to Get Clean Books and{" "}
          <span className="text-yellow-300">Peace of Mind?</span>
        </h2>
        
        <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
          Schedule your free assessment today. No pressure, no sales pitch — just clarity.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          {benefits.map((benefit, index) => (
            <div key={index} className="flex items-center gap-2 text-white/90">
              <CheckCircle className="w-5 h-5 text-green-300" />
              <span>{benefit}</span>
            </div>
          ))}
        </div>

        <Button
          onClick={onGetAssessment}
          className="bg-white hover:bg-gray-100 text-blue-600 px-8 py-6 rounded-full text-xl font-semibold shadow-lg transform transition-all duration-200 hover:scale-105"
        >
          Get Your Free Assessment
          <ArrowRight className="ml-2 w-5 h-5" />
        </Button>
      </div>
    </section>
  );
 
